import GifImage from './GifImage';
import SmartAvatar from './SmartAvatar';
import LazyImage from './LazyImage';
import { formatTime } from '@/utils/dateUtils';
import { memo } from 'react';

export interface PinData {
  id: string;
  content: string;
  contentType: 'text' | 'image' | 'gif';
  imageUrl?: string;
  timestamp: string;
  setter: {
    name: string;
    avatar: string;
  };
  sender: {
    name: string;
    avatar: string;
  };
  groupName?: string;
}

interface PinCardProps {
  pin: PinData;
}

const PinCard = memo(function PinCard({ pin }: PinCardProps) {

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md dark:hover:shadow-lg theme-transition overflow-hidden card-hover">
      {/* 顶部用户信息栏 */}
      <div className="flex items-center justify-between p-3 border-b border-gray-100 dark:border-gray-700">
        <div className="flex items-center space-x-3">
          {/* 发送者头像 */}
          <SmartAvatar
            nickname={pin.sender.name}
            size={32}
            className="flex-shrink-0"
          />

          {/* 用户信息 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <span className="font-medium text-gray-900 dark:text-white text-sm truncate theme-transition">
                {pin.sender.name}
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 theme-transition">
                发送
              </span>
            </div>
            <div className="flex items-center space-x-2 mt-0.5">
              <span className="text-xs text-gray-500 dark:text-gray-400 theme-transition">
                {formatTime(pin.timestamp)}
              </span>
              <span className="text-xs text-gray-400 dark:text-gray-500 theme-transition">
                由
              </span>
              <span className="text-xs text-blue-600 dark:text-blue-400 font-medium theme-transition">
                {pin.setter.name}
              </span>
              <span className="text-xs text-gray-400 dark:text-gray-500 theme-transition">
                设为精华
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="p-3">
        {/* 文本内容 */}
        {pin.content && (
          <div className="mb-3">
            <p className="text-gray-900 dark:text-gray-100 text-sm leading-relaxed break-words theme-transition">
              {pin.content}
            </p>
          </div>
        )}

        {/* 图片/动图内容 */}
        {(pin.contentType === 'image' || pin.contentType === 'gif') && pin.imageUrl ? (
          <div className="rounded-lg overflow-hidden">
            {pin.contentType === 'gif' ? (
              <GifImage
                src={pin.imageUrl}
                alt="精华动图"
                className="w-full"
              />
            ) : (
              <LazyImage
                src={pin.imageUrl}
                alt="精华内容"
                width={400}
                height={300}
                className="w-full h-auto object-cover"
              />
            )}
          </div>
        ) : null}
      </div>
    </div>
  );
});

export default PinCard;
