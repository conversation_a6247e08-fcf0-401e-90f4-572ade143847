/**
 * 防闪烁主题脚本
 * 在页面加载时立即应用主题，防止主题切换时的闪烁
 */
export function ThemeScript() {
  const script = `
    (function() {
      try {
        const storageKey = 'q-pins-hub-theme';
        const defaultTheme = 'system';
        
        // 获取系统主题偏好
        function getSystemTheme() {
          return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        }
        
        // 解析主题
        function resolveTheme(theme) {
          if (theme === 'system') {
            return getSystemTheme();
          }
          return theme;
        }
        
        // 应用主题
        function applyTheme(resolvedTheme) {
          const root = document.documentElement;

          // Tailwind CSS只需要dark类，浅色模式时移除dark类即可
          if (resolvedTheme === 'dark') {
            root.classList.add('dark');
          } else {
            root.classList.remove('dark');
          }

          // 更新CSS变量
          if (resolvedTheme === 'dark') {
            root.style.setProperty('--background', '#0a0a0a');
            root.style.setProperty('--foreground', '#ededed');
          } else {
            root.style.setProperty('--background', '#ffffff');
            root.style.setProperty('--foreground', '#171717');
          }
        }
        
        // 从localStorage读取主题
        let savedTheme = defaultTheme;
        try {
          const stored = localStorage.getItem(storageKey);
          if (stored && ['light', 'dark', 'system'].includes(stored)) {
            savedTheme = stored;
          }
        } catch (error) {
          // localStorage不可用时使用默认主题
        }
        
        // 立即应用主题
        const resolved = resolveTheme(savedTheme);
        applyTheme(resolved);
        
      } catch (error) {
        // 出错时应用默认亮色主题（移除dark类即可）
        document.documentElement.classList.remove('dark');
      }
    })();
  `;

  return (
    <script
      dangerouslySetInnerHTML={{ __html: script }}
      suppressHydrationWarning
    />
  );
}
