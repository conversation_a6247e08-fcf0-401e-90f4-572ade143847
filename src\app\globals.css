@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* 亮色主题颜色变量 */
  --card-background: #ffffff;
  --card-border: #e5e7eb;
  --header-background: #ffffff;
  --header-border: #e5e7eb;
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  --input-background: #ffffff;
  --input-border: #d1d5db;
  --button-background: #f3f4f6;
  --button-hover: #e5e7eb;
  --loading-background: #f3f4f6;
  --error-background: #fef2f2;
  --error-text: #dc2626;
}

/* 暗色主题颜色变量 */
.dark {
  --background: #0a0a0a;
  --foreground: #ededed;

  --card-background: #1a1a1a;
  --card-border: #2a2a2a;
  --header-background: #111111;
  --header-border: #2a2a2a;
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-muted: #9ca3af;
  --input-background: #1f1f1f;
  --input-border: #374151;
  --button-background: #1f2937;
  --button-hover: #374151;
  --loading-background: #1f2937;
  --error-background: #1f1f1f;
  --error-text: #f87171;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.dark ::-webkit-scrollbar-track {
  background: #1f1f1f;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
  background: #4b5563;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 图片加载动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* 卡片悬停效果增强 */
.card-hover {
  transition: all 0.2s ease-in-out;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.dark .card-hover:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* 响应式文字大小 */
@media (max-width: 640px) {
  body {
    font-size: 14px;
  }
}

/* 焦点样式优化 */
.focus-ring:focus {
  outline: none;
  ring: 2px;
  ring-color: #3b82f6;
  ring-offset: 2px;
}

.dark .focus-ring:focus {
  ring-color: #60a5fa;
}

/* 主题切换过渡动画 */
.theme-transition {
  transition: background-color 0.2s ease-in-out,
              color 0.2s ease-in-out,
              border-color 0.2s ease-in-out,
              box-shadow 0.2s ease-in-out;
}

/* 加载状态动画优化 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin 2s linear infinite;
}

/* 暗色模式下的选择文本样式 */
.dark ::selection {
  background-color: #3b82f6;
  color: #ffffff;
}

::selection {
  background-color: #3b82f6;
  color: #ffffff;
}
