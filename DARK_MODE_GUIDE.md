# Q精华Hub 暗色模式使用指南

## 功能概述

Q精华Hub现已完全支持暗色模式，提供了优雅的主题切换体验和完整的暗色主题适配。

## 主要特性

### 🌓 主题切换
- **三种主题模式**：浅色、深色、跟随系统
- **智能切换**：支持系统主题偏好检测
- **持久化存储**：主题偏好自动保存到localStorage
- **防闪烁机制**：页面加载时立即应用主题，避免闪烁

### 🎨 视觉设计
- **深色背景**：使用纯黑色(#0a0a0a)作为主背景，确保良好的对比度
- **统一色彩**：所有组件都适配了暗色模式，保持视觉一致性
- **平滑过渡**：主题切换时有200ms的平滑过渡动画
- **优化对比度**：确保暗色模式下的文字可读性

### 🔧 技术实现
- **React Context**：使用Context API管理全局主题状态
- **Tailwind CSS**：利用Tailwind的dark:前缀实现样式切换
- **TypeScript**：完整的类型安全支持
- **SSR兼容**：支持服务端渲染，防止水合错误

## 使用方法

### 主题切换按钮
主题切换按钮位于页面右下角，提供三个选项：
- **☀️ 浅色**：传统的亮色主题
- **🌙 深色**：深色主题，适合夜间使用
- **💻 跟随系统**：自动跟随系统主题设置

### 快捷键支持
目前暂不支持快捷键切换，但可以通过点击切换按钮来改变主题。

## 组件适配

### 已适配的组件
- ✅ **Header组件**：顶部导航栏、搜索框、选择器
- ✅ **PinCard组件**：精华消息卡片、用户信息、时间戳
- ✅ **MasonryGrid组件**：瀑布流布局、加载状态、空状态
- ✅ **LazyImage组件**：图片懒加载、占位符、错误状态
- ✅ **SmartAvatar组件**：智能头像、加载状态
- ✅ **GifImage组件**：GIF动图、加载状态、错误状态
- ✅ **LoadingSpinner组件**：加载动画
- ✅ **ThemeToggle组件**：主题切换按钮

### 样式特点
- **卡片背景**：从白色(#ffffff)切换到深灰色(#1a1a1a)
- **边框颜色**：从浅灰色(#e5e7eb)切换到深灰色(#2a2a2a)
- **文字颜色**：从深色(#111827)切换到浅色(#f9fafb)
- **次要文字**：从中灰色(#6b7280)切换到浅灰色(#d1d5db)

## 开发指南

### 添加暗色模式支持
为新组件添加暗色模式支持时，请遵循以下模式：

```tsx
// 基础样式 + 暗色模式样式 + 过渡动画
className="bg-white dark:bg-gray-800 text-gray-900 dark:text-white theme-transition"
```

### CSS变量
全局CSS变量已在`globals.css`中定义：
```css
:root {
  --background: #ffffff;
  --foreground: #171717;
  /* 其他亮色主题变量 */
}

.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
  /* 其他暗色主题变量 */
}
```

### 主题Context使用
```tsx
import { useThemeContext } from '@/components/ThemeProvider';

function MyComponent() {
  const { theme, setTheme, resolvedTheme } = useThemeContext();
  
  return (
    <div className={`theme-transition ${resolvedTheme === 'dark' ? 'dark-specific-class' : 'light-specific-class'}`}>
      {/* 组件内容 */}
    </div>
  );
}
```

## 浏览器兼容性

- ✅ **Chrome 88+**：完全支持
- ✅ **Firefox 89+**：完全支持
- ✅ **Safari 14+**：完全支持
- ✅ **Edge 88+**：完全支持

## 性能优化

- **防闪烁脚本**：页面加载时立即应用主题
- **localStorage缓存**：主题偏好持久化存储
- **CSS变量**：高效的样式切换机制
- **过渡动画**：200ms的平滑过渡，不影响性能

## 故障排除

### 主题不生效
1. 检查浏览器是否支持CSS自定义属性
2. 确认localStorage是否可用
3. 检查控制台是否有JavaScript错误

### 闪烁问题
1. 确认ThemeScript组件已正确添加到<head>中
2. 检查suppressHydrationWarning属性是否设置

### 样式不一致
1. 确认所有组件都使用了theme-transition类
2. 检查CSS变量是否正确定义
3. 验证Tailwind的dark:前缀是否正确使用

## 更新日志

### v1.0.0 (2024-01-27)
- ✨ 初始暗色模式实现
- ✨ 三种主题模式支持
- ✨ 完整组件适配
- ✨ 防闪烁机制
- ✨ 主题切换动画
