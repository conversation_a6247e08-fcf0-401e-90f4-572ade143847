'use client';

import { useState, useEffect, memo } from 'react';
import Image from 'next/image';
import { getAvatarPath, checkAvatarExists } from '@/services/avatarService';
import { generateSVGAvatar } from '@/utils/avatarUtils';
import { avatarCache } from '@/services/avatarCacheService';

interface SmartAvatarProps {
  nickname: string;
  size?: number;
  className?: string;
}

const SmartAvatar = memo(function SmartAvatar({
  nickname,
  size = 32,
  className = ''
}: SmartAvatarProps) {
  const [avatarSrc, setAvatarSrc] = useState<string>(generateSVGAvatar(nickname));
  const [isLoading, setIsLoading] = useState(false); // 默认不显示加载状态
  const [hasError, setHasError] = useState(false);
  const [loadAttempted, setLoadAttempted] = useState(false); // 防止重复加载

  useEffect(() => {
    // 防止重复加载和React StrictMode双重调用
    if (loadAttempted) return;

    const loadRealAvatar = async () => {
      try {
        setLoadAttempted(true);
        setIsLoading(true);
        setHasError(false);

        // 尝试获取真实头像路径
        const avatarPath = await getAvatarPath(nickname);

        if (avatarPath) {
          // 检查头像文件是否存在（使用请求管理器）
          const exists = await checkAvatarExists(avatarPath);

          if (exists) {
            // 使用缓存服务获取头像URL
            const cachedUrl = await avatarCache.getAvatarUrl(avatarPath);
            setAvatarSrc(cachedUrl);
          } else {
            // 头像文件不存在，保持使用SVG头像
            console.debug(`头像文件不存在: ${avatarPath}, 使用SVG头像`);
          }
        }

      } catch (error) {
        console.warn('加载真实头像失败:', error);
        setHasError(true);
        // 保持使用SVG头像
      } finally {
        setIsLoading(false);
      }
    };

    // 添加延迟以避免请求风暴
    const timeoutId = setTimeout(() => {
      loadRealAvatar();
    }, Math.random() * 100); // 0-100ms随机延迟

    return () => clearTimeout(timeoutId);
  }, [nickname, loadAttempted]);

  const handleImageError = () => {
    console.warn(`头像加载失败，回退到SVG: ${nickname}`);
    setAvatarSrc(generateSVGAvatar(nickname));
    setHasError(true);
  };

  const handleImageLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  // 判断是否为SVG数据URI
  const isSVGDataUri = avatarSrc.startsWith('data:image/svg+xml');

  return (
    <div className={`relative overflow-hidden rounded-full ${className}`} style={{ width: size, height: size }}>
      {isSVGDataUri ? (
        // SVG头像直接使用img标签
        <img
          src={avatarSrc}
          alt={nickname}
          className="w-full h-full object-cover"
          style={{ width: size, height: size }}
        />
      ) : (
        // 真实头像使用Next.js Image组件
        <Image
          src={avatarSrc}
          alt={nickname}
          width={size}
          height={size}
          className="w-full h-full object-cover"
          onError={handleImageError}
          onLoad={handleImageLoad}
          priority={size >= 64} // 大头像优先加载
        />
      )}
      
      {/* 加载状态指示器（仅在加载真实头像时显示） */}
      {isLoading && !isSVGDataUri && (
        <div className="absolute inset-0 bg-gray-200 dark:bg-gray-700 flex items-center justify-center theme-transition">
          <div className="w-3 h-3 border border-gray-400 dark:border-gray-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
    </div>
  );
});

export default SmartAvatar;
