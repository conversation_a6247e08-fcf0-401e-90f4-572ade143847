'use client';

import { useThemeContext } from '@/components/ThemeProvider';
import { useEffect, useState } from 'react';

export default function ThemeDebug() {
  const { theme, resolvedTheme } = useThemeContext();
  const [htmlClasses, setHtmlClasses] = useState('');
  const [cssVars, setCssVars] = useState({ background: '', foreground: '' });

  useEffect(() => {
    // 获取HTML元素的类名
    setHtmlClasses(document.documentElement.className);
    
    // 获取CSS变量值
    const styles = getComputedStyle(document.documentElement);
    setCssVars({
      background: styles.getPropertyValue('--background'),
      foreground: styles.getPropertyValue('--foreground')
    });
  }, [theme, resolvedTheme]);

  return (
    <div className="fixed top-4 left-4 z-50 p-4 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg text-xs font-mono">
      <h3 className="font-bold mb-2 text-gray-900 dark:text-white">主题调试信息</h3>
      <div className="space-y-1 text-gray-700 dark:text-gray-300">
        <div>当前主题: {theme}</div>
        <div>解析主题: {resolvedTheme}</div>
        <div>HTML类名: {htmlClasses || '无'}</div>
        <div>--background: {cssVars.background}</div>
        <div>--foreground: {cssVars.foreground}</div>
        <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded">
          <div>这是一个测试区域</div>
          <div className="text-blue-600 dark:text-blue-400">蓝色文字测试</div>
        </div>
      </div>
    </div>
  );
}
