// 头像缓存服务

// 缓存项接口
interface AvatarCacheItem {
  url: string;
  timestamp: number;
  blob?: Blob; // 用于存储图片数据
}

// 缓存配置
const CACHE_CONFIG = {
  maxSize: 100, // 最大缓存数量
  maxAge: 24 * 60 * 60 * 1000, // 24小时过期
  preloadBatchSize: 5, // 预加载批次大小
};

// 内存缓存
class AvatarMemoryCache {
  private cache = new Map<string, AvatarCacheItem>();
  private accessOrder: string[] = []; // LRU访问顺序

  // 获取缓存
  get(key: string): string | null {
    const item = this.cache.get(key);
    if (!item) return null;

    // 检查是否过期
    if (Date.now() - item.timestamp > CACHE_CONFIG.maxAge) {
      this.delete(key);
      return null;
    }

    // 更新访问顺序（LRU）
    this.updateAccessOrder(key);
    return item.url;
  }

  // 设置缓存
  set(key: string, url: string, blob?: Blob): void {
    // 如果缓存已满，删除最久未使用的项
    if (this.cache.size >= CACHE_CONFIG.maxSize) {
      const oldestKey = this.accessOrder[0];
      this.delete(oldestKey);
    }

    const item: AvatarCacheItem = {
      url,
      timestamp: Date.now(),
      blob
    };

    this.cache.set(key, item);
    this.updateAccessOrder(key);
  }

  // 删除缓存
  delete(key: string): void {
    this.cache.delete(key);
    const index = this.accessOrder.indexOf(key);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
  }

  // 更新访问顺序
  private updateAccessOrder(key: string): void {
    const index = this.accessOrder.indexOf(key);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
    this.accessOrder.push(key);
  }

  // 清空缓存
  clear(): void {
    this.cache.clear();
    this.accessOrder = [];
  }

  // 获取缓存统计（仅开发环境）
  getStats() {
    if (process.env.NODE_ENV === 'development') {
      return {
        size: this.cache.size,
        maxSize: CACHE_CONFIG.maxSize,
        keys: Array.from(this.cache.keys())
      };
    }
    return { size: this.cache.size };
  }

  // 清理过期缓存
  cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    this.cache.forEach((item, key) => {
      if (now - item.timestamp > CACHE_CONFIG.maxAge) {
        expiredKeys.push(key);
      }
    });

    expiredKeys.forEach(key => this.delete(key));
  }
}

// 全局缓存实例
const memoryCache = new AvatarMemoryCache();

// IndexedDB缓存（持久化）
class AvatarIndexedDBCache {
  private dbName = 'AvatarCache';
  private storeName = 'avatars';
  private version = 1;
  private db: IDBDatabase | null = null;

  // 初始化数据库
  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, { keyPath: 'key' });
          store.createIndex('timestamp', 'timestamp', { unique: false });
        }
      };
    });
  }

  // 获取缓存
  async get(key: string): Promise<Blob | null> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      const request = store.get(key);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        const result = request.result;
        if (!result) {
          resolve(null);
          return;
        }

        // 检查是否过期
        if (Date.now() - result.timestamp > CACHE_CONFIG.maxAge) {
          this.delete(key);
          resolve(null);
          return;
        }

        resolve(result.blob);
      };
    });
  }

  // 设置缓存
  async set(key: string, blob: Blob): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      const item = {
        key,
        blob,
        timestamp: Date.now()
      };

      const request = store.put(item);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  // 删除缓存
  async delete(key: string): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const request = store.delete(key);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  // 清理过期缓存
  async cleanup(): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const index = store.index('timestamp');
      
      const cutoffTime = Date.now() - CACHE_CONFIG.maxAge;
      const range = IDBKeyRange.upperBound(cutoffTime);
      
      const request = index.openCursor(range);
      request.onerror = () => reject(request.error);
      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          cursor.delete();
          cursor.continue();
        } else {
          resolve();
        }
      };
    });
  }
}

// 全局IndexedDB缓存实例
const indexedDBCache = new AvatarIndexedDBCache();

// 头像预加载队列
class AvatarPreloader {
  private queue: string[] = [];
  private loading = new Set<string>();

  // 添加到预加载队列
  add(urls: string[]): void {
    urls.forEach(url => {
      if (!this.queue.includes(url) && !this.loading.has(url)) {
        this.queue.push(url);
      }
    });
    this.processQueue();
  }

  // 处理预加载队列
  private async processQueue(): Promise<void> {
    while (this.queue.length > 0 && this.loading.size < CACHE_CONFIG.preloadBatchSize) {
      const url = this.queue.shift()!;
      this.loading.add(url);

      try {
        await this.preloadImage(url);
      } catch (error) {
        console.warn('预加载头像失败:', url, error);
      } finally {
        this.loading.delete(url);
      }
    }
  }

  // 预加载单个图片
  private async preloadImage(url: string): Promise<void> {
    // 检查内存缓存
    if (memoryCache.get(url)) return;

    try {
      const response = await fetch(url);
      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      const blob = await response.blob();
      const objectUrl = URL.createObjectURL(blob);

      // 存储到缓存
      memoryCache.set(url, objectUrl, blob);
      await indexedDBCache.set(url, blob);

    } catch (error) {
      throw error;
    }
  }
}

// 全局预加载器实例
const preloader = new AvatarPreloader();

// 主要的头像缓存API
export const avatarCache = {
  // 获取头像URL（优先从缓存）
  async getAvatarUrl(originalUrl: string): Promise<string> {
    // 1. 检查内存缓存
    const cachedUrl = memoryCache.get(originalUrl);
    if (cachedUrl) {
      return cachedUrl;
    }

    // 2. 检查IndexedDB缓存
    try {
      const blob = await indexedDBCache.get(originalUrl);
      if (blob) {
        const objectUrl = URL.createObjectURL(blob);
        memoryCache.set(originalUrl, objectUrl, blob);
        return objectUrl;
      }
    } catch (error) {
      console.warn('IndexedDB缓存读取失败:', error);
    }

    // 3. 从网络加载并缓存
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

      const response = await fetch(originalUrl, {
        signal: controller.signal,
        cache: 'force-cache' // 强制使用浏览器缓存
      });

      clearTimeout(timeoutId);

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      const blob = await response.blob();
      const objectUrl = URL.createObjectURL(blob);

      // 存储到缓存
      memoryCache.set(originalUrl, objectUrl, blob);

      // 异步存储到IndexedDB，不阻塞返回
      indexedDBCache.set(originalUrl, blob).catch(err => {
        console.warn('IndexedDB存储失败:', err);
      });

      return objectUrl;
    } catch (error) {
      // 缓存失败结果，避免重复请求
      memoryCache.set(originalUrl, originalUrl);
      console.warn('头像加载失败，使用原始URL:', originalUrl, error);
      return originalUrl;
    }
  },

  // 预加载头像
  preload(urls: string[]): void {
    preloader.add(urls);
  },

  // 清理缓存
  async cleanup(): Promise<void> {
    memoryCache.cleanup();
    await indexedDBCache.cleanup();
  },

  // 清空所有缓存
  async clear(): Promise<void> {
    memoryCache.clear();
    // IndexedDB清空需要删除数据库
    try {
      await indexedDBCache.cleanup();
    } catch (error) {
      console.warn('清空IndexedDB缓存失败:', error);
    }
  },

  // 获取缓存统计（仅开发环境）
  getStats() {
    if (process.env.NODE_ENV === 'development') {
      return {
        memory: memoryCache.getStats(),
        preloadQueue: 0 // 暂时移除私有属性访问
      };
    }
    return null;
  }
};

// 定期清理过期缓存
setInterval(() => {
  avatarCache.cleanup();
}, 60 * 60 * 1000); // 每小时清理一次
